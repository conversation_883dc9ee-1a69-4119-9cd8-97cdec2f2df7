import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../models/device_model.dart';
import '../models/video_player_model.dart';
import '../widgets/video_player_widget.dart';
import '../widgets/audio_control_widget.dart';
import '../services/remote_control_service.dart' as remote;
import 'device_selection_screen.dart';

/// 主界面 - 多路视频播放
class MainScreen extends StatefulWidget {
  const MainScreen({Key? key}) : super(key: key);

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with remote.RemoteNavigationMixin {
  late DeviceManager _deviceManager;
  late MultiVideoPlayerManager _videoManager;
  bool _showDeviceSelection = false;
  final FocusNode _mainFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _deviceManager = DeviceManager();
    _videoManager = MultiVideoPlayerManager();

    // 注册焦点节点
    remote.FocusManager.registerFocusNode('main', _mainFocusNode);

    // 自动发现设备
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _discoverDevices();
      _mainFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    remote.FocusManager.unregisterFocusNode('main');
    _mainFocusNode.dispose();
    _deviceManager.dispose();
    _videoManager.dispose();
    super.dispose();
  }

  @override
  void onRemoteKeyEvent(remote.RemoteKeyEvent event) {
    if (_showDeviceSelection) {
      // 设备选择界面处理按键事件
      return;
    }

    switch (event.keyType) {
      case remote.RemoteKeyType.up:
        _navigatePlayer(-2); // 上移（2x2网格中向上一行）
        break;
      case remote.RemoteKeyType.down:
        _navigatePlayer(2); // 下移（2x2网格中向下一行）
        break;
      case remote.RemoteKeyType.left:
        _navigatePlayer(-1); // 左移
        break;
      case remote.RemoteKeyType.right:
        _navigatePlayer(1); // 右移
        break;
      case remote.RemoteKeyType.center:
        if (event.isLongPress) {
          _showMainContextMenu();
        }
        break;
      case remote.RemoteKeyType.menu:
        if (event.isLongPress) {
          _showAudioControl();
        } else {
          _showChannelSelection(null);
        }
        break;
      case remote.RemoteKeyType.back:
        if (_showDeviceSelection) {
          setState(() {
            _showDeviceSelection = false;
          });
        }
        break;
      case remote.RemoteKeyType.channelUp:
        _switchToNextChannel();
        break;
      case remote.RemoteKeyType.channelDown:
        _switchToPreviousChannel();
        break;
      case remote.RemoteKeyType.number1:
        _switchLayout(2);
        break;
      case remote.RemoteKeyType.number2:
        _switchLayout(4);
        break;
      case remote.RemoteKeyType.mute:
        _videoManager.toggleGlobalMute();
        break;
      default:
        break;
    }
  }

  Future<void> _discoverDevices() async {
    await _deviceManager.discoverDevices();
    if (_deviceManager.hasDevices) {
      await _deviceManager.connectAllDevices();
    }
  }

  /// 导航到指定播放器
  void _navigatePlayer(int delta) {
    final currentIndex = _videoManager.selectedPlayerIndex;
    final playerCount = _videoManager.players.length;

    int newIndex;

    if (_videoManager.currentLayout == 2) {
      // 2路布局：垂直排列
      newIndex = (currentIndex + delta).clamp(0, playerCount - 1);
    } else {
      // 4路布局：2x2网格
      if (delta == -2 || delta == 2) {
        // 上下移动
        final currentRow = currentIndex ~/ 2;
        final currentCol = currentIndex % 2;
        final newRow = (currentRow + (delta ~/ 2)).clamp(0, 1);
        newIndex = newRow * 2 + currentCol;
      } else {
        // 左右移动
        final currentRow = currentIndex ~/ 2;
        final currentCol = currentIndex % 2;
        final newCol = (currentCol + delta).clamp(0, 1);
        newIndex = currentRow * 2 + newCol;
      }
    }

    _videoManager.selectPlayer(newIndex);
  }

  /// 切换到下一个可用通道
  void _switchToNextChannel() {
    final availableChannels = _deviceManager.getAllAvailableChannels();
    if (availableChannels.isEmpty) return;

    // 简单实现：选择第一个可用通道
    if (availableChannels.isNotEmpty) {
      _videoManager.playChannelInSelected(availableChannels.first);
    }
  }

  /// 切换到上一个可用通道
  void _switchToPreviousChannel() {
    final availableChannels = _deviceManager.getAllAvailableChannels();
    if (availableChannels.isEmpty) return;

    // 简单实现：选择最后一个可用通道
    if (availableChannels.isNotEmpty) {
      _videoManager.playChannelInSelected(availableChannels.last);
    }
  }

  /// 切换布局
  void _switchLayout(int layout) {
    _videoManager.switchLayout(layout);
  }

  /// 显示音频控制弹窗
  void _showAudioControl() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AudioControlDialog(
        videoManager: _videoManager,
      ),
    );
  }

  /// 显示主菜单
  void _showMainContextMenu() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => MainContextMenuDialog(
        videoManager: _videoManager,
        deviceManager: _deviceManager,
        onChannelSelection: () => _showChannelSelection(null),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: _deviceManager),
        ChangeNotifierProvider.value(value: _videoManager),
      ],
      child: Focus(
        focusNode: _mainFocusNode,
        onKeyEvent: (node, event) => remote.RemoteControlService.handleKeyEvent(event),
        child: Scaffold(
          backgroundColor: Colors.black,
          body: SafeArea(
            child: Stack(
              children: [
                _buildMainContent(),
                if (_showDeviceSelection) _buildDeviceSelectionOverlay(),
                _buildTopBar(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 60, 16, 16),
      child: Consumer<MultiVideoPlayerManager>(
        builder: (context, videoManager, child) {
          return MultiVideoPlayerGrid(
            players: videoManager.players,
            selectedIndex: videoManager.selectedPlayerIndex,
            onPlayerSelected: (index) {
              videoManager.selectPlayer(index);
            },
            onPlayerLongPress: (index) {
              _showChannelSelection(index);
            },
          );
        },
      ),
    );
  }

  Widget _buildTopBar() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        height: 60,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.8),
          border: Border(
            bottom: BorderSide(color: Colors.grey.withOpacity(0.3)),
          ),
        ),
        child: Row(
          children: [
            const Text(
              'LinkEye 监控',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            _buildLayoutSwitcher(),
            const SizedBox(width: 16),
            _buildConnectionStatus(),
          ],
        ),
      ),
    );
  }



  Widget _buildLayoutSwitcher() {
    return Consumer<MultiVideoPlayerManager>(
      builder: (context, videoManager, child) {
        return Row(
          children: [
            _buildLayoutButton(2, videoManager.currentLayout == 2),
            const SizedBox(width: 8),
            _buildLayoutButton(4, videoManager.currentLayout == 4),
          ],
        );
      },
    );
  }

  Widget _buildLayoutButton(int layout, bool isSelected) {
    return GestureDetector(
      onTap: () => _videoManager.switchLayout(layout),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue : Colors.transparent,
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          '${layout}路',
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey,
            fontSize: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildConnectionStatus() {
    return Consumer<DeviceManager>(
      builder: (context, deviceManager, child) {
        final connectedCount = deviceManager.connectedDevices.length;
        final totalCount = deviceManager.devices.length;
        
        return Row(
          children: [
            Icon(
              connectedCount > 0 ? Icons.wifi : Icons.wifi_off,
              color: connectedCount > 0 ? Colors.green : Colors.red,
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              '$connectedCount/$totalCount',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ],
        );
      },
    );
  }



  Widget _buildDeviceSelectionOverlay() {
    return Positioned.fill(
      child: Container(
        color: Colors.black.withOpacity(0.8),
        child: DeviceSelectionScreen(
          deviceManager: _deviceManager,
          onChannelSelected: (channelInfo) {
            _videoManager.playChannelInSelected(channelInfo);
            setState(() {
              _showDeviceSelection = false;
            });
          },
          onClose: () {
            setState(() {
              _showDeviceSelection = false;
            });
          },
        ),
      ),
    );
  }

  void _showChannelSelection(int? playerIndex) {
    setState(() {
      _showDeviceSelection = true;
    });
  }
}
