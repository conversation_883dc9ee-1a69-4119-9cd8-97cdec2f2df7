import 'package:flutter/foundation.dart';
import 'package:video_player/video_player.dart';
import '../services/hikvision_service.dart';
import 'device_model.dart';

/// 视频播放状态
enum VideoPlayStatus {
  idle,
  loading,
  playing,
  paused,
  error,
  stopped,
}

/// 单个视频播放器模型
class VideoPlayerModel extends ChangeNotifier {
  VideoPlayerController? _controller;
  VideoPlayStatus _status = VideoPlayStatus.idle;
  CameraChannelInfo? _channelInfo;
  VideoPlayInfo? _playInfo;
  String? _errorMessage;
  bool _isMuted = true; // 默认静音

  // Getters
  VideoPlayerController? get controller => _controller;
  VideoPlayStatus get status => _status;
  CameraChannelInfo? get channelInfo => _channelInfo;
  VideoPlayInfo? get playInfo => _playInfo;
  String? get errorMessage => _errorMessage;
  bool get isMuted => _isMuted;
  bool get isPlaying => _status == VideoPlayStatus.playing;
  bool get hasVideo => _channelInfo != null;
  String get displayName => _channelInfo?.displayName ?? '未选择';

  void _setStatus(VideoPlayStatus status, [String? errorMessage]) {
    _status = status;
    _errorMessage = errorMessage;
    notifyListeners();
  }

  /// 播放指定通道的视频
  Future<bool> playChannel(CameraChannelInfo channelInfo) async {
    if (_status == VideoPlayStatus.loading) {
      return false;
    }

    // 先停止当前播放
    await stop();

    _setStatus(VideoPlayStatus.loading);
    _channelInfo = channelInfo;

    try {
      // 通过海康威视服务开始播放
      final playInfo = await HikVisionService.startPreview(
        channelInfo.deviceIp,
        channelInfo.channelId,
        username: channelInfo.device.username,
        password: channelInfo.device.password,
      );

      if (playInfo == null) {
        _setStatus(VideoPlayStatus.error, '无法开始视频播放');
        return false;
      }

      _playInfo = playInfo;

      // 创建视频播放器控制器
      _controller = VideoPlayerController.networkUrl(
        Uri.parse(playInfo.rtspUrl),
      );

      // 初始化播放器
      await _controller!.initialize();
      
      // 设置静音状态
      await _controller!.setVolume(_isMuted ? 0.0 : 1.0);
      
      // 开始播放
      await _controller!.play();
      
      _setStatus(VideoPlayStatus.playing);
      return true;

    } catch (e) {
      _setStatus(VideoPlayStatus.error, '播放失败: $e');
      await _cleanup();
      return false;
    }
  }

  /// 停止播放
  Future<void> stop() async {
    if (_controller != null) {
      await _controller!.pause();
      await _controller!.dispose();
      _controller = null;
    }

    if (_playInfo != null) {
      await HikVisionService.stopPreview(_playInfo!.playHandle);
      _playInfo = null;
    }

    _channelInfo = null;
    _setStatus(VideoPlayStatus.stopped);
  }

  /// 暂停播放
  Future<void> pause() async {
    if (_controller != null && _status == VideoPlayStatus.playing) {
      await _controller!.pause();
      _setStatus(VideoPlayStatus.paused);
    }
  }

  /// 恢复播放
  Future<void> resume() async {
    if (_controller != null && _status == VideoPlayStatus.paused) {
      await _controller!.play();
      _setStatus(VideoPlayStatus.playing);
    }
  }

  /// 切换静音状态
  Future<void> toggleMute() async {
    _isMuted = !_isMuted;
    if (_controller != null) {
      await _controller!.setVolume(_isMuted ? 0.0 : 1.0);
    }
    notifyListeners();
  }

  /// 设置音量
  Future<void> setVolume(double volume) async {
    if (_controller != null) {
      await _controller!.setVolume(volume);
      _isMuted = volume == 0.0;
      notifyListeners();
    }
  }

  /// 清理资源
  Future<void> _cleanup() async {
    if (_controller != null) {
      await _controller!.dispose();
      _controller = null;
    }
    _playInfo = null;
  }

  @override
  void dispose() {
    _cleanup();
    super.dispose();
  }
}

/// 多路视频播放管理器
class MultiVideoPlayerManager extends ChangeNotifier {
  final List<VideoPlayerModel> _players = [];
  int _currentLayout = 4; // 默认4路布局
  int _selectedPlayerIndex = 0;
  bool _globalMute = true; // 全局静音

  // Getters
  List<VideoPlayerModel> get players => List.unmodifiable(_players);
  int get currentLayout => _currentLayout;
  int get selectedPlayerIndex => _selectedPlayerIndex;
  bool get globalMute => _globalMute;
  VideoPlayerModel get selectedPlayer => _players[_selectedPlayerIndex];
  int get playingCount => _players.where((p) => p.isPlaying).length;

  MultiVideoPlayerManager() {
    _initializePlayers();
  }

  /// 初始化播放器
  void _initializePlayers() {
    _players.clear();
    for (int i = 0; i < _currentLayout; i++) {
      final player = VideoPlayerModel();
      player.addListener(() => notifyListeners());
      _players.add(player);
    }
    notifyListeners();
  }

  /// 切换布局（2路或4路）
  void switchLayout(int layout) {
    if (layout != 2 && layout != 4) {
      return;
    }

    if (_currentLayout == layout) {
      return;
    }

    // 停止所有当前播放
    stopAllPlayers();

    _currentLayout = layout;
    _selectedPlayerIndex = 0;
    _initializePlayers();
  }

  /// 选择播放器
  void selectPlayer(int index) {
    if (index >= 0 && index < _players.length) {
      _selectedPlayerIndex = index;
      notifyListeners();
    }
  }

  /// 在选中的播放器中播放通道
  Future<bool> playChannelInSelected(CameraChannelInfo channelInfo) async {
    return await _players[_selectedPlayerIndex].playChannel(channelInfo);
  }

  /// 在指定播放器中播放通道
  Future<bool> playChannelInPlayer(int playerIndex, CameraChannelInfo channelInfo) async {
    if (playerIndex >= 0 && playerIndex < _players.length) {
      return await _players[playerIndex].playChannel(channelInfo);
    }
    return false;
  }

  /// 停止指定播放器
  Future<void> stopPlayer(int index) async {
    if (index >= 0 && index < _players.length) {
      await _players[index].stop();
    }
  }

  /// 停止所有播放器
  Future<void> stopAllPlayers() async {
    final futures = _players.map((player) => player.stop()).toList();
    await Future.wait(futures);
  }

  /// 切换全局静音
  Future<void> toggleGlobalMute() async {
    _globalMute = !_globalMute;
    
    final futures = _players.map((player) async {
      if (player.isPlaying) {
        await player.setVolume(_globalMute ? 0.0 : 1.0);
      }
    }).toList();
    
    await Future.wait(futures);
    notifyListeners();
  }

  /// 设置全局音量
  Future<void> setGlobalVolume(double volume) async {
    _globalMute = volume == 0.0;
    
    final futures = _players.map((player) async {
      if (player.isPlaying) {
        await player.setVolume(volume);
      }
    }).toList();
    
    await Future.wait(futures);
    notifyListeners();
  }

  /// 获取播放器网格位置
  int getPlayerRow(int index) {
    if (_currentLayout == 2) {
      return index; // 2路：垂直排列
    } else {
      return index ~/ 2; // 4路：2x2网格
    }
  }

  int getPlayerColumn(int index) {
    if (_currentLayout == 2) {
      return 0; // 2路：单列
    } else {
      return index % 2; // 4路：2列
    }
  }

  /// 获取下一个可用的播放器索引
  int? getNextAvailablePlayerIndex() {
    for (int i = 0; i < _players.length; i++) {
      if (!_players[i].hasVideo) {
        return i;
      }
    }
    return null;
  }

  /// 自动分配通道到播放器
  Future<bool> autoAssignChannel(CameraChannelInfo channelInfo) async {
    final availableIndex = getNextAvailablePlayerIndex();
    if (availableIndex != null) {
      return await playChannelInPlayer(availableIndex, channelInfo);
    }
    
    // 如果没有可用播放器，替换当前选中的播放器
    return await playChannelInSelected(channelInfo);
  }

  @override
  void dispose() {
    for (final player in _players) {
      player.dispose();
    }
    _players.clear();
    super.dispose();
  }
}
