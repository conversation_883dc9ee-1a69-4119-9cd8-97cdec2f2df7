# 设备连接设置指南

## 手动添加录像机设备

现在您可以通过以下两种方式添加海康威视录像机：

### 方式1：使用设备测试界面（推荐新手）

1. **启动应用**
   - 打开 LinkEye 应用
   - 在首页选择 "设备连接测试"

2. **输入设备信息**
   - **IP地址**: 输入您的录像机IP地址（例如：*************）
   - **用户名**: 默认为 `admin`，如果您修改过请输入正确的用户名
   - **密码**: 输入您的录像机密码

3. **连接测试**
   - 点击 "连接设备" 按钮
   - 等待连接结果
   - 如果连接成功，会显示找到的摄像头数量

4. **查看结果**
   - 连接成功的设备会显示在下方列表中
   - 可以看到设备状态、摄像头数量等信息

### 方式2：在监控界面中添加

1. **进入监控界面**
   - 在首页选择 "进入监控界面"
   - 按遥控器的 "菜单键" 打开摄像头选择界面

2. **添加设备**
   - 在摄像头选择界面，点击右上角的 "添加设备" 按钮
   - 填写设备信息：
     - IP地址（必填）
     - 设备名称（可选，默认为 NVR-IP地址）
     - 用户名（默认 admin）
     - 密码（必填）

3. **连接验证**
   - 点击 "添加" 按钮
   - 系统会自动测试连接并获取摄像头列表
   - 连接成功后设备会出现在左侧设备列表中

## 使用示例

### 代码示例
```dart
// 直接使用服务类连接
List<CameraChannel> cameras = await HikVisionService.getDeviceChannels(
  '*************',           // 您的录像机IP
  username: 'admin',         // 用户名
  password: 'your_password', // 您的密码
);

// 使用设备管理器
final deviceManager = DeviceManager();

// 手动添加设备
final device = HikVisionDevice(
  ip: '*************',
  name: '客厅录像机',
  model: 'DS-7608NI-K2/8P',
  serialNumber: 'Unknown',
  httpPort: 80,
);

deviceManager.addDevice(device);

// 设置凭据并连接
final deviceModel = deviceManager.getDevice('*************');
if (deviceModel != null) {
  deviceModel.setCredentials('admin', 'your_password');
  bool connected = await deviceModel.connect();
  
  if (connected) {
    print('连接成功！摄像头数量: ${deviceModel.channels.length}');
    for (var channel in deviceModel.channels) {
      print('通道 ${channel.id}: ${channel.name}');
    }
  }
}
```

## 常见问题

### 1. 连接失败
**可能原因：**
- IP地址错误
- 用户名或密码错误
- 网络不通
- 设备未启用网络服务

**解决方案：**
- 确认IP地址正确（可以ping测试）
- 检查用户名密码
- 确保设备和手机/电脑在同一网络
- 检查设备的网络配置

### 2. 找不到摄像头
**可能原因：**
- 设备连接成功但没有配置摄像头
- 摄像头通道被禁用
- 权限不足

**解决方案：**
- 登录设备web界面检查摄像头配置
- 启用相应的摄像头通道
- 确认用户权限包含摄像头访问

### 3. IP地址格式
**正确格式：**
- *************
- *********
- ************

**错误格式：**
- 192.168.1 (缺少最后一段)
- 192.168.1.300 (数字超出范围)
- http://************* (包含协议)

## 网络要求

- 设备和应用需要在同一局域网内
- 录像机需要启用以下服务：
  - HTTP服务（端口80）
  - RTSP服务（端口554）
  - 设备发现服务（端口37020）

## 默认设置

- **默认用户名**: admin
- **默认密码**: 12345（请根据实际情况修改）
- **HTTP端口**: 80
- **RTSP端口**: 554
