import 'package:flutter/material.dart';
import '../models/device_model.dart';
import '../services/hikvision_service.dart';

/// 设备测试页面 - 用于测试手动添加设备功能
class DeviceTestScreen extends StatefulWidget {
  const DeviceTestScreen({super.key});

  @override
  State<DeviceTestScreen> createState() => _DeviceTestScreenState();
}

class _DeviceTestScreenState extends State<DeviceTestScreen> {
  late DeviceManager _deviceManager;
  final TextEditingController _ipController = TextEditingController();
  final TextEditingController _usernameController = TextEditingController(text: 'admin');
  final TextEditingController _passwordController = TextEditingController(text: '12345');
  bool _isConnecting = false;
  String? _statusMessage;

  @override
  void initState() {
    super.initState();
    _deviceManager = DeviceManager();
  }

  @override
  void dispose() {
    _ipController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _deviceManager.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设备连接测试'),
        backgroundColor: Colors.grey[900],
      ),
      backgroundColor: Colors.black,
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              '手动添加海康威视录像机',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 32),
            
            // IP地址输入
            _buildInputField(
              controller: _ipController,
              label: 'IP地址',
              hint: '例如: *************',
              icon: Icons.router,
            ),
            const SizedBox(height: 16),
            
            // 用户名输入
            _buildInputField(
              controller: _usernameController,
              label: '用户名',
              hint: '默认: admin',
              icon: Icons.person,
            ),
            const SizedBox(height: 16),
            
            // 密码输入
            _buildInputField(
              controller: _passwordController,
              label: '密码',
              hint: '设备登录密码',
              icon: Icons.lock,
              obscureText: true,
            ),
            const SizedBox(height: 32),
            
            // 连接按钮
            ElevatedButton(
              onPressed: _isConnecting ? null : _connectToDevice,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isConnecting
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(width: 12),
                        Text('连接中...'),
                      ],
                    )
                  : const Text(
                      '连接设备',
                      style: TextStyle(fontSize: 16),
                    ),
            ),
            
            const SizedBox(height: 24),
            
            // 状态信息
            if (_statusMessage != null)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _statusMessage!.contains('成功') 
                      ? Colors.green.withValues(alpha: 0.2)
                      : Colors.red.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _statusMessage!,
                  style: TextStyle(
                    color: _statusMessage!.contains('成功') 
                        ? Colors.green 
                        : Colors.red,
                    fontSize: 14,
                  ),
                ),
              ),
            
            const SizedBox(height: 24),
            
            // 设备列表
            Expanded(
              child: _buildDeviceList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    bool obscureText = false,
  }) {
    return TextField(
      controller: controller,
      obscureText: obscureText,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        labelStyle: const TextStyle(color: Colors.white70),
        hintStyle: const TextStyle(color: Colors.white54),
        prefixIcon: Icon(icon, color: Colors.white70),
        enabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: Colors.white54),
          borderRadius: BorderRadius.circular(8),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: Colors.blue),
          borderRadius: BorderRadius.circular(8),
        ),
        filled: true,
        fillColor: Colors.grey[800],
      ),
    );
  }

  Widget _buildDeviceList() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              '已连接的设备',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: _deviceManager.devices.isEmpty
                ? const Center(
                    child: Text(
                      '暂无设备\n请添加录像机IP地址',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 16,
                      ),
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _deviceManager.devices.length,
                    itemBuilder: (context, index) {
                      final device = _deviceManager.devices[index];
                      return _buildDeviceItem(device);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceItem(DeviceModel device) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: device.isConnected 
            ? Colors.green.withValues(alpha: 0.2)
            : Colors.red.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: device.isConnected ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                device.isConnected ? Icons.check_circle : Icons.error,
                color: device.isConnected ? Colors.green : Colors.red,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                device.device.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                device.device.ip,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          if (device.isConnected) ...[
            const SizedBox(height: 8),
            Text(
              '摄像头数量: ${device.channels.length}',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
            if (device.channels.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                '启用通道: ${device.getEnabledChannels().length}',
                style: const TextStyle(
                  color: Colors.green,
                  fontSize: 12,
                ),
              ),
            ],
          ],
          if (device.errorMessage != null) ...[
            const SizedBox(height: 8),
            Text(
              '错误: ${device.errorMessage}',
              style: const TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _connectToDevice() async {
    final ip = _ipController.text.trim();
    final username = _usernameController.text.trim();
    final password = _passwordController.text.trim();

    if (ip.isEmpty) {
      setState(() {
        _statusMessage = '请输入IP地址';
      });
      return;
    }

    if (!_isValidIP(ip)) {
      setState(() {
        _statusMessage = '请输入有效的IP地址';
      });
      return;
    }

    setState(() {
      _isConnecting = true;
      _statusMessage = null;
    });

    try {
      // 创建设备
      final device = HikVisionDevice(
        ip: ip,
        name: 'NVR-$ip',
        model: 'Unknown',
        serialNumber: 'Unknown',
        httpPort: 80,
      );

      // 添加到设备管理器
      _deviceManager.addDevice(device);

      // 获取设备模型并设置凭据
      final deviceModel = _deviceManager.getDevice(ip);
      if (deviceModel != null) {
        deviceModel.setCredentials(username, password);
        
        // 尝试连接
        final connected = await deviceModel.connect();
        
        setState(() {
          if (connected) {
            _statusMessage = '连接成功！找到 ${deviceModel.channels.length} 个摄像头';
          } else {
            _statusMessage = '连接失败，请检查IP地址、用户名和密码';
            _deviceManager.removeDevice(ip);
          }
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = '连接失败: $e';
      });
    } finally {
      setState(() {
        _isConnecting = false;
      });
    }
  }

  bool _isValidIP(String ip) {
    final parts = ip.split('.');
    if (parts.length != 4) return false;
    
    for (final part in parts) {
      final num = int.tryParse(part);
      if (num == null || num < 0 || num > 255) return false;
    }
    return true;
  }
}
