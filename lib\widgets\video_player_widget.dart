import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import '../models/video_player_model.dart';

/// 单个视频播放器组件
class VideoPlayerWidget extends StatefulWidget {
  final VideoPlayerModel playerModel;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const VideoPlayerWidget({
    Key? key,
    required this.playerModel,
    this.isSelected = false,
    this.onTap,
    this.onLongPress,
  }) : super(key: key);

  @override
  State<VideoPlayerWidget> createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  @override
  void initState() {
    super.initState();
    widget.playerModel.addListener(_onPlayerStateChanged);
  }

  @override
  void dispose() {
    widget.playerModel.removeListener(_onPlayerStateChanged);
    super.dispose();
  }

  void _onPlayerStateChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      onLongPress: widget.onLongPress,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: widget.isSelected ? Colors.blue : Colors.grey,
            width: widget.isSelected ? 3.0 : 1.0,
          ),
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8.0),
          child: Stack(
            children: [
              _buildVideoContent(),
              _buildOverlay(),
              if (widget.isSelected) _buildSelectionIndicator(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoContent() {
    final controller = widget.playerModel.controller;
    
    if (controller != null && controller.value.isInitialized) {
      return AspectRatio(
        aspectRatio: controller.value.aspectRatio,
        child: VideoPlayer(controller),
      );
    }
    
    return Container(
      color: Colors.black,
      child: const Center(
        child: Icon(
          Icons.videocam_off,
          color: Colors.grey,
          size: 48,
        ),
      ),
    );
  }

  Widget _buildOverlay() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withOpacity(0.3),
            ],
          ),
        ),
        child: Column(
          children: [
            _buildTopBar(),
            const Spacer(),
            _buildBottomBar(),
          ],
        ),
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        children: [
          Expanded(
            child: Text(
              widget.playerModel.displayName,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          _buildStatusIcon(),
        ],
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        children: [
          _buildMuteIcon(),
          const Spacer(),
          _buildPlayStatusText(),
        ],
      ),
    );
  }

  Widget _buildStatusIcon() {
    IconData icon;
    Color color;

    switch (widget.playerModel.status) {
      case VideoPlayStatus.loading:
        return const SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        );
      case VideoPlayStatus.playing:
        icon = Icons.play_circle_filled;
        color = Colors.green;
        break;
      case VideoPlayStatus.paused:
        icon = Icons.pause_circle_filled;
        color = Colors.orange;
        break;
      case VideoPlayStatus.error:
        icon = Icons.error;
        color = Colors.red;
        break;
      case VideoPlayStatus.stopped:
        icon = Icons.stop_circle;
        color = Colors.grey;
        break;
      default:
        icon = Icons.radio_button_unchecked;
        color = Colors.grey;
    }

    return Icon(
      icon,
      color: color,
      size: 16,
    );
  }

  Widget _buildMuteIcon() {
    if (!widget.playerModel.isPlaying) {
      return const SizedBox.shrink();
    }

    return Icon(
      widget.playerModel.isMuted ? Icons.volume_off : Icons.volume_up,
      color: Colors.white,
      size: 16,
    );
  }

  Widget _buildPlayStatusText() {
    String statusText;
    
    switch (widget.playerModel.status) {
      case VideoPlayStatus.loading:
        statusText = '加载中...';
        break;
      case VideoPlayStatus.playing:
        statusText = '播放中';
        break;
      case VideoPlayStatus.paused:
        statusText = '已暂停';
        break;
      case VideoPlayStatus.error:
        statusText = '错误';
        break;
      case VideoPlayStatus.stopped:
        statusText = '已停止';
        break;
      default:
        statusText = '未播放';
    }

    return Text(
      statusText,
      style: const TextStyle(
        color: Colors.white,
        fontSize: 10,
      ),
    );
  }

  Widget _buildSelectionIndicator() {
    return Positioned(
      top: 8,
      right: 8,
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.blue,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Icon(
          Icons.check,
          color: Colors.white,
          size: 16,
        ),
      ),
    );
  }
}

/// 多路视频播放网格组件
class MultiVideoPlayerGrid extends StatelessWidget {
  final List<VideoPlayerModel> players;
  final int selectedIndex;
  final Function(int) onPlayerSelected;
  final Function(int)? onPlayerLongPress;

  const MultiVideoPlayerGrid({
    Key? key,
    required this.players,
    required this.selectedIndex,
    required this.onPlayerSelected,
    this.onPlayerLongPress,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final layout = players.length;
    
    if (layout == 2) {
      return _build2x1Layout();
    } else if (layout == 4) {
      return _build2x2Layout();
    } else {
      return const Center(
        child: Text('不支持的布局'),
      );
    }
  }

  Widget _build2x1Layout() {
    return Column(
      children: [
        Expanded(
          child: _buildPlayerWidget(0),
        ),
        const SizedBox(height: 8),
        Expanded(
          child: _buildPlayerWidget(1),
        ),
      ],
    );
  }

  Widget _build2x2Layout() {
    return Column(
      children: [
        Expanded(
          child: Row(
            children: [
              Expanded(child: _buildPlayerWidget(0)),
              const SizedBox(width: 8),
              Expanded(child: _buildPlayerWidget(1)),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Expanded(
          child: Row(
            children: [
              Expanded(child: _buildPlayerWidget(2)),
              const SizedBox(width: 8),
              Expanded(child: _buildPlayerWidget(3)),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPlayerWidget(int index) {
    if (index >= players.length) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Text('无效播放器'),
        ),
      );
    }

    return VideoPlayerWidget(
      playerModel: players[index],
      isSelected: index == selectedIndex,
      onTap: () => onPlayerSelected(index),
      onLongPress: onPlayerLongPress != null 
          ? () => onPlayerLongPress!(index)
          : null,
    );
  }
}
