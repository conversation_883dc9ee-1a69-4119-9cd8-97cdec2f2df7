import 'package:flutter/foundation.dart';
import '../services/hikvision_service.dart';

/// 设备连接状态
enum DeviceStatus {
  unknown,
  connecting,
  connected,
  disconnected,
  error,
}

/// 设备模型
class DeviceModel extends ChangeNotifier {
  final HikVisionDevice device;
  DeviceStatus _status = DeviceStatus.unknown;
  List<CameraChannel> _channels = [];
  String _username = 'admin';
  String _password = '12345';
  String? _errorMessage;

  DeviceModel(this.device);

  // Getters
  DeviceStatus get status => _status;
  List<CameraChannel> get channels => List.unmodifiable(_channels);
  String get username => _username;
  String get password => _password;
  String? get errorMessage => _errorMessage;
  bool get isConnected => _status == DeviceStatus.connected;
  bool get hasChannels => _channels.isNotEmpty;

  // Setters
  void setCredentials(String username, String password) {
    _username = username;
    _password = password;
    notifyListeners();
  }

  void _setStatus(DeviceStatus status, [String? errorMessage]) {
    _status = status;
    _errorMessage = errorMessage;
    notifyListeners();
  }

  /// 连接设备并获取通道信息
  Future<bool> connect() async {
    if (_status == DeviceStatus.connecting) {
      return false;
    }

    _setStatus(DeviceStatus.connecting);

    try {
      // 测试连接
      final isConnected = await HikVisionService.testConnection(
        device.ip,
        username: _username,
        password: _password,
      );

      if (!isConnected) {
        _setStatus(DeviceStatus.error, '无法连接到设备');
        return false;
      }

      // 获取通道信息
      final channels = await HikVisionService.getDeviceChannels(
        device.ip,
        username: _username,
        password: _password,
      );

      _channels = channels;
      _setStatus(DeviceStatus.connected);
      return true;

    } catch (e) {
      _setStatus(DeviceStatus.error, '连接失败: $e');
      return false;
    }
  }

  /// 断开连接
  void disconnect() {
    _channels.clear();
    _setStatus(DeviceStatus.disconnected);
  }

  /// 刷新通道信息
  Future<bool> refreshChannels() async {
    if (_status != DeviceStatus.connected) {
      return false;
    }

    try {
      final channels = await HikVisionService.getDeviceChannels(
        device.ip,
        username: _username,
        password: _password,
      );

      _channels = channels;
      notifyListeners();
      return true;

    } catch (e) {
      _setStatus(DeviceStatus.error, '刷新通道失败: $e');
      return false;
    }
  }

  /// 获取指定ID的通道
  CameraChannel? getChannel(int channelId) {
    try {
      return _channels.firstWhere((channel) => channel.id == channelId);
    } catch (e) {
      return null;
    }
  }

  /// 获取启用的通道列表
  List<CameraChannel> getEnabledChannels() {
    return _channels.where((channel) => channel.enabled).toList();
  }

  @override
  String toString() {
    return 'DeviceModel{device: ${device.ip}, status: $_status, channels: ${_channels.length}}';
  }
}

/// 设备管理器
class DeviceManager extends ChangeNotifier {
  final List<DeviceModel> _devices = [];
  bool _isDiscovering = false;
  String? _errorMessage;

  // Getters
  List<DeviceModel> get devices => List.unmodifiable(_devices);
  bool get isDiscovering => _isDiscovering;
  String? get errorMessage => _errorMessage;
  bool get hasDevices => _devices.isNotEmpty;
  List<DeviceModel> get connectedDevices => 
      _devices.where((device) => device.isConnected).toList();

  /// 发现网络中的设备
  Future<void> discoverDevices() async {
    if (_isDiscovering) {
      return;
    }

    _isDiscovering = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final discoveredDevices = await HikVisionService.discoverDevices();
      
      // 清除旧设备列表
      _devices.clear();
      
      // 添加新发现的设备
      for (final device in discoveredDevices) {
        _devices.add(DeviceModel(device));
      }

      _errorMessage = null;

    } catch (e) {
      _errorMessage = '设备发现失败: $e';
    } finally {
      _isDiscovering = false;
      notifyListeners();
    }
  }

  /// 添加设备（手动添加）
  void addDevice(HikVisionDevice device) {
    // 检查是否已存在
    final exists = _devices.any((d) => d.device.ip == device.ip);
    if (!exists) {
      _devices.add(DeviceModel(device));
      notifyListeners();
    }
  }

  /// 移除设备
  void removeDevice(String ip) {
    final index = _devices.indexWhere((device) => device.device.ip == ip);
    if (index != -1) {
      _devices[index].disconnect();
      _devices.removeAt(index);
      notifyListeners();
    }
  }

  /// 获取指定IP的设备
  DeviceModel? getDevice(String ip) {
    try {
      return _devices.firstWhere((device) => device.device.ip == ip);
    } catch (e) {
      return null;
    }
  }

  /// 连接所有设备
  Future<void> connectAllDevices() async {
    final futures = _devices.map((device) => device.connect()).toList();
    await Future.wait(futures);
  }

  /// 断开所有设备
  void disconnectAllDevices() {
    for (final device in _devices) {
      device.disconnect();
    }
    notifyListeners();
  }

  /// 获取所有可用的摄像头通道
  List<CameraChannelInfo> getAllAvailableChannels() {
    final channels = <CameraChannelInfo>[];
    
    for (final device in _devices) {
      if (device.isConnected) {
        for (final channel in device.getEnabledChannels()) {
          channels.add(CameraChannelInfo(
            device: device,
            channel: channel,
          ));
        }
      }
    }
    
    return channels;
  }

  @override
  void dispose() {
    disconnectAllDevices();
    super.dispose();
  }
}

/// 摄像头通道信息（包含设备信息）
class CameraChannelInfo {
  final DeviceModel device;
  final CameraChannel channel;

  CameraChannelInfo({
    required this.device,
    required this.channel,
  });

  String get displayName => '${device.device.name} - ${channel.name}';
  String get deviceIp => device.device.ip;
  int get channelId => channel.id;
  String get rtspUrl => channel.rtspUrl;

  @override
  String toString() {
    return 'CameraChannelInfo{device: ${device.device.ip}, channel: ${channel.id}}';
  }
}
