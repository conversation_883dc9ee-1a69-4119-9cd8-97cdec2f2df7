# LinkEye 故障排除指南

## 常见问题解决方案

### 1. MissingPluginException 错误

**问题描述:**
```
设备发现失败: MissingPluginException(No implementation found for method discoverDevices on channel com.linkeye.link_eye/hikvision)
```

**原因分析:**
这个错误表明Flutter无法找到原生Android插件的实现。这通常发生在以下情况：
- 应用是第一次运行，原生代码还未编译
- 热重载无法更新原生代码
- 插件注册有问题

**解决方案:**

#### 方案1: 完全重新构建应用（推荐）
```bash
# 清理项目
flutter clean

# 重新获取依赖
flutter pub get

# 完全重新构建（不要使用热重载）
flutter run --no-hot
```

#### 方案2: 检查网络和设备配置
如果原生插件不可用，请检查以下配置：

- 确保设备在同一网络中
- 检查设备的网络发现功能是否启用
- 验证设备的用户名和密码是否正确

### 2. 视频播放问题

**问题描述:**
选择摄像头后视频无法播放或显示黑屏。

**可能原因:**
1. RTSP流地址不正确
2. 网络连接问题
3. 设备认证失败
4. 视频编码不支持

**解决方案:**

#### 检查RTSP流
```bash
# 使用VLC或其他播放器测试RTSP流
vlc rtsp://admin:12345@*************:554/Streaming/Channels/101
```

#### 检查网络连接
```bash
# 测试设备连通性
ping *************

# 测试RTSP端口
telnet ************* 554
```

#### 修改认证信息
在 `lib/services/hikvision_service.dart` 中修改默认用户名密码：
```dart
static Future<List<CameraChannel>> getDeviceChannels(
  String ip, {
  String username = 'your_username',  // 修改这里
  String password = 'your_password',  // 修改这里
}) async {
```

### 3. 遥控器无响应

**问题描述:**
Android TV遥控器按键无响应。

**解决方案:**

#### 检查焦点状态
确保应用获得了焦点：
- 点击应用界面
- 按遥控器的确认键

#### 支持的按键映射
- **方向键**: 导航选择
- **确认键/选择键**: 确认操作
- **菜单键**: 打开摄像头选择（短按）/ 音频控制（长按）
- **返回键/ESC键**: 退出当前界面
- **数字键1**: 切换到2路布局
- **数字键2**: 切换到4路布局
- **静音键**: 切换全局静音

### 4. 设备发现问题

**问题描述:**
无法发现网络中的海康威视设备。

**解决方案:**

#### 网络检查
1. 确保设备在同一网段
2. 检查防火墙设置
3. 确认设备启用了网络发现功能

#### 手动添加设备
如果自动发现失败，可以在代码中手动添加设备：

```dart
// 在DeviceManager中手动添加设备
final deviceManager = DeviceManager();
deviceManager.addDevice(HikVisionDevice(
  ip: '你的设备IP',
  name: '你的设备名称',
  model: '设备型号',
  serialNumber: '序列号',
  httpPort: 80,
));
```

### 5. 音频控制问题

**问题描述:**
音频控制功能无效或无声音。

**解决方案:**

#### 检查音频权限
确保应用有音频播放权限（通常自动获得）。

#### 使用音频控制
- 长按菜单键打开音频控制面板
- 使用方向键选择选项
- 使用左右键调节音量

#### 单独控制每路音频
1. 长按菜单键
2. 选择"单独控制"
3. 使用上下键选择播放器
4. 使用左右键调节该播放器音量
5. 按确认键切换静音状态

### 6. 布局切换问题

**问题描述:**
无法在2路和4路布局间切换。

**解决方案:**

#### 使用数字键切换
- 按数字键1：切换到2路布局
- 按数字键2：切换到4路布局

#### 使用界面按钮
点击顶部状态栏的"2路"或"4路"按钮。

### 7. 性能优化

**问题描述:**
多路视频播放时出现卡顿或延迟。

**解决方案:**

#### 降低视频质量
修改RTSP URL使用较低的码流：
```dart
// 主码流（高质量）
rtsp://admin:12345@ip:554/Streaming/Channels/101

// 子码流（低质量，推荐用于多路播放）
rtsp://admin:12345@ip:554/Streaming/Channels/102
```

#### 减少同时播放路数
在性能较低的设备上，建议使用2路布局而不是4路。

### 8. 开发调试

**启用详细日志:**
应用使用Logger包记录详细日志，可以通过以下方式查看：

```bash
# 查看Flutter日志
flutter logs

# 查看Android日志
adb logcat | grep flutter
```

**调试模式运行:**
```bash
# 以调试模式运行
flutter run --debug

# 查看性能信息
flutter run --profile
```

### 9. 生产环境部署

**构建发布版本:**
```bash
# 构建发布版APK
flutter build apk --release

# 构建Android App Bundle（推荐）
flutter build appbundle --release
```

**签名配置:**
在生产环境中，需要配置应用签名。参考Flutter官方文档进行配置。

## 联系支持

如果以上解决方案都无法解决问题，请：

1. 收集错误日志
2. 记录复现步骤
3. 提供设备信息（Android版本、设备型号等）
4. 提供网络环境信息
5. 通过GitHub Issues提交问题报告

## 更新日志

### v1.0.0
- 初始版本发布
- 支持海康威视设备发现
- 支持2路/4路视频播放
- 支持Android TV遥控器操作
- 支持音频控制
- 支持真实设备连接
