import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../models/video_player_model.dart';
import '../models/device_model.dart';
import '../services/remote_control_service.dart';

/// 音频控制弹窗
class AudioControlDialog extends StatefulWidget {
  final MultiVideoPlayerManager videoManager;

  const AudioControlDialog({
    Key? key,
    required this.videoManager,
  }) : super(key: key);

  @override
  State<AudioControlDialog> createState() => _AudioControlDialogState();
}

class _AudioControlDialogState extends State<AudioControlDialog> 
    with RemoteNavigationMixin {
  final FocusNode _focusNode = FocusNode();
  int _selectedOption = 0; // 0: 全局静音, 1: 音量调节, 2: 单独控制
  double _globalVolume = 0.0;

  @override
  void initState() {
    super.initState();
    _globalVolume = widget.videoManager.globalMute ? 0.0 : 1.0;
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  void onRemoteKeyEvent(RemoteKeyEvent event) {
    switch (event.keyType) {
      case RemoteKeyType.up:
        setState(() {
          _selectedOption = (_selectedOption - 1).clamp(0, 2);
        });
        break;
      case RemoteKeyType.down:
        setState(() {
          _selectedOption = (_selectedOption + 1).clamp(0, 2);
        });
        break;
      case RemoteKeyType.left:
        if (_selectedOption == 1) {
          _adjustVolume(-0.1);
        }
        break;
      case RemoteKeyType.right:
        if (_selectedOption == 1) {
          _adjustVolume(0.1);
        }
        break;
      case RemoteKeyType.center:
        _executeSelectedOption();
        break;
      case RemoteKeyType.back:
      case RemoteKeyType.menu:
        Navigator.of(context).pop();
        break;
      default:
        break;
    }
  }

  void _adjustVolume(double delta) {
    setState(() {
      _globalVolume = (_globalVolume + delta).clamp(0.0, 1.0);
    });
    widget.videoManager.setGlobalVolume(_globalVolume);
  }

  void _executeSelectedOption() {
    switch (_selectedOption) {
      case 0:
        widget.videoManager.toggleGlobalMute();
        setState(() {
          _globalVolume = widget.videoManager.globalMute ? 0.0 : 1.0;
        });
        break;
      case 1:
        // 音量调节在左右键中处理
        break;
      case 2:
        Navigator.of(context).pop();
        _showIndividualAudioControl();
        break;
    }
  }

  void _showIndividualAudioControl() {
    showDialog(
      context: context,
      builder: (context) => IndividualAudioControlDialog(
        videoManager: widget.videoManager,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      child: Dialog(
        backgroundColor: Colors.black.withOpacity(0.9),
        child: Container(
          width: 400,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              const SizedBox(height: 24),
              _buildOptions(),
              const SizedBox(height: 24),
              _buildInstructions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(
          Icons.volume_up,
          color: Colors.white,
          size: 32,
        ),
        const SizedBox(width: 12),
        const Text(
          '音频控制',
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        GestureDetector(
          onTap: () => Navigator.of(context).pop(),
          child: const Icon(
            Icons.close,
            color: Colors.white,
            size: 24,
          ),
        ),
      ],
    );
  }

  Widget _buildOptions() {
    return Column(
      children: [
        _buildOption(
          index: 0,
          icon: widget.videoManager.globalMute ? Icons.volume_off : Icons.volume_up,
          title: '全局静音',
          subtitle: widget.videoManager.globalMute ? '已静音' : '有声音',
        ),
        const SizedBox(height: 12),
        _buildVolumeOption(),
        const SizedBox(height: 12),
        _buildOption(
          index: 2,
          icon: Icons.tune,
          title: '单独控制',
          subtitle: '分别控制每路音频',
        ),
      ],
    );
  }

  Widget _buildOption({
    required int index,
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    final isSelected = _selectedOption == index;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.withOpacity(0.3) : Colors.transparent,
        border: Border.all(
          color: isSelected ? Colors.blue : Colors.grey.withOpacity(0.3),
          width: isSelected ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: isSelected ? Colors.white : Colors.grey,
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.grey[300],
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: isSelected ? Colors.white70 : Colors.grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          if (isSelected)
            const Icon(
              Icons.chevron_right,
              color: Colors.white,
              size: 20,
            ),
        ],
      ),
    );
  }

  Widget _buildVolumeOption() {
    final isSelected = _selectedOption == 1;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.withOpacity(0.3) : Colors.transparent,
        border: Border.all(
          color: isSelected ? Colors.blue : Colors.grey.withOpacity(0.3),
          width: isSelected ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.volume_up,
                color: isSelected ? Colors.white : Colors.grey,
                size: 24,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '音量调节',
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.grey[300],
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '使用左右键调节音量',
                      style: TextStyle(
                        color: isSelected ? Colors.white70 : Colors.grey,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                const Icon(
                  Icons.chevron_right,
                  color: Colors.white,
                  size: 20,
                ),
            ],
          ),
          if (isSelected) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(
                  Icons.volume_down,
                  color: Colors.white70,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: Colors.blue,
                      inactiveTrackColor: Colors.grey,
                      thumbColor: Colors.white,
                      overlayColor: Colors.blue.withOpacity(0.3),
                    ),
                    child: Slider(
                      value: _globalVolume,
                      onChanged: (value) {
                        setState(() {
                          _globalVolume = value;
                        });
                        widget.videoManager.setGlobalVolume(value);
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                const Icon(
                  Icons.volume_up,
                  color: Colors.white70,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  '${(_globalVolume * 100).round()}%',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return const Text(
      '使用上下键选择选项，左右键调节音量，回车键确认，菜单键或返回键退出',
      style: TextStyle(
        color: Colors.grey,
        fontSize: 12,
      ),
      textAlign: TextAlign.center,
    );
  }
}

/// 单独音频控制弹窗
class IndividualAudioControlDialog extends StatefulWidget {
  final MultiVideoPlayerManager videoManager;

  const IndividualAudioControlDialog({
    Key? key,
    required this.videoManager,
  }) : super(key: key);

  @override
  State<IndividualAudioControlDialog> createState() => _IndividualAudioControlDialogState();
}

class _IndividualAudioControlDialogState extends State<IndividualAudioControlDialog> 
    with RemoteNavigationMixin {
  final FocusNode _focusNode = FocusNode();
  int _selectedPlayerIndex = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  void onRemoteKeyEvent(RemoteKeyEvent event) {
    switch (event.keyType) {
      case RemoteKeyType.up:
        setState(() {
          _selectedPlayerIndex = (_selectedPlayerIndex - 1)
              .clamp(0, widget.videoManager.players.length - 1);
        });
        break;
      case RemoteKeyType.down:
        setState(() {
          _selectedPlayerIndex = (_selectedPlayerIndex + 1)
              .clamp(0, widget.videoManager.players.length - 1);
        });
        break;
      case RemoteKeyType.left:
        _adjustPlayerVolume(-0.1);
        break;
      case RemoteKeyType.right:
        _adjustPlayerVolume(0.1);
        break;
      case RemoteKeyType.center:
        _togglePlayerMute();
        break;
      case RemoteKeyType.back:
      case RemoteKeyType.menu:
        Navigator.of(context).pop();
        break;
      default:
        break;
    }
  }

  void _adjustPlayerVolume(double delta) {
    final player = widget.videoManager.players[_selectedPlayerIndex];
    if (player.controller != null) {
      final currentVolume = player.isMuted ? 0.0 : 1.0;
      final newVolume = (currentVolume + delta).clamp(0.0, 1.0);
      player.setVolume(newVolume);
    }
  }

  void _togglePlayerMute() {
    final player = widget.videoManager.players[_selectedPlayerIndex];
    player.toggleMute();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      child: Dialog(
        backgroundColor: Colors.black.withOpacity(0.9),
        child: Container(
          width: 500,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              const SizedBox(height: 24),
              _buildPlayerList(),
              const SizedBox(height: 24),
              _buildInstructions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(
          Icons.tune,
          color: Colors.white,
          size: 32,
        ),
        const SizedBox(width: 12),
        const Text(
          '单独音频控制',
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        GestureDetector(
          onTap: () => Navigator.of(context).pop(),
          child: const Icon(
            Icons.close,
            color: Colors.white,
            size: 24,
          ),
        ),
      ],
    );
  }

  Widget _buildPlayerList() {
    return Column(
      children: List.generate(
        widget.videoManager.players.length,
        (index) => _buildPlayerItem(index),
      ),
    );
  }

  Widget _buildPlayerItem(int index) {
    final player = widget.videoManager.players[index];
    final isSelected = _selectedPlayerIndex == index;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.withOpacity(0.3) : Colors.transparent,
        border: Border.all(
          color: isSelected ? Colors.blue : Colors.grey.withOpacity(0.3),
          width: isSelected ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Text(
            '播放器 ${index + 1}',
            style: TextStyle(
              color: isSelected ? Colors.white : Colors.grey[300],
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              player.hasVideo ? player.displayName : '未播放',
              style: TextStyle(
                color: isSelected ? Colors.white70 : Colors.grey,
                fontSize: 14,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 16),
          Icon(
            player.isMuted ? Icons.volume_off : Icons.volume_up,
            color: isSelected ? Colors.white : Colors.grey,
            size: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return const Text(
      '使用上下键选择播放器，左右键调节音量，回车键切换静音，返回键退出',
      style: TextStyle(
        color: Colors.grey,
        fontSize: 12,
      ),
      textAlign: TextAlign.center,
    );
  }
}

/// OK键长按弹出的主菜单
class MainContextMenuDialog extends StatefulWidget {
  final MultiVideoPlayerManager videoManager;
  final DeviceManager deviceManager;
  final VoidCallback onChannelSelection;

  const MainContextMenuDialog({
    Key? key,
    required this.videoManager,
    required this.deviceManager,
    required this.onChannelSelection,
  }) : super(key: key);

  @override
  State<MainContextMenuDialog> createState() => _MainContextMenuDialogState();
}

class _MainContextMenuDialogState extends State<MainContextMenuDialog>
    with RemoteNavigationMixin {
  final FocusNode _focusNode = FocusNode();
  int _selectedOption = 0; // 0: 选择摄像头, 1: 静音/取消静音

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  void onRemoteKeyEvent(RemoteKeyEvent event) {
    switch (event.keyType) {
      case RemoteKeyType.up:
        setState(() {
          _selectedOption = (_selectedOption - 1).clamp(0, 1);
        });
        break;
      case RemoteKeyType.down:
        setState(() {
          _selectedOption = (_selectedOption + 1).clamp(0, 1);
        });
        break;
      case RemoteKeyType.center:
        _executeSelectedOption();
        break;
      case RemoteKeyType.back:
      case RemoteKeyType.menu:
        Navigator.of(context).pop();
        break;
      default:
        break;
    }
  }

  void _executeSelectedOption() {
    switch (_selectedOption) {
      case 0:
        // 选择摄像头
        Navigator.of(context).pop();
        widget.onChannelSelection();
        break;
      case 1:
        // 静音/取消静音
        widget.videoManager.toggleGlobalMute();
        Navigator.of(context).pop();
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      child: Dialog(
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        child: Container(
          width: 300,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                '主菜单',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),
              _buildMenuOption(
                index: 0,
                icon: Icons.videocam,
                title: '选择摄像头',
                isSelected: _selectedOption == 0,
              ),
              const SizedBox(height: 12),
              Consumer<MultiVideoPlayerManager>(
                builder: (context, videoManager, child) {
                  return _buildMenuOption(
                    index: 1,
                    icon: videoManager.globalMute ? Icons.volume_off : Icons.volume_up,
                    title: videoManager.globalMute ? '取消静音' : '静音',
                    isSelected: _selectedOption == 1,
                  );
                },
              ),
              const SizedBox(height: 24),
              _buildInstructions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuOption({
    required int index,
    required IconData icon,
    required String title,
    required bool isSelected,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.withValues(alpha: 0.3) : Colors.transparent,
        border: Border.all(
          color: isSelected ? Colors.blue : Colors.grey.withValues(alpha: 0.3),
          width: isSelected ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: isSelected ? Colors.blue : Colors.white,
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                color: isSelected ? Colors.blue : Colors.white,
                fontSize: 16,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return const Text(
      '使用上下键选择选项，回车键确认，返回键或菜单键退出',
      style: TextStyle(
        color: Colors.grey,
        fontSize: 12,
      ),
      textAlign: TextAlign.center,
    );
  }
}
