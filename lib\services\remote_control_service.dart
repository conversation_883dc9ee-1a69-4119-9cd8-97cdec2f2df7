import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

/// 遥控器按键类型
enum RemoteKeyType {
  up,
  down,
  left,
  right,
  center,
  back,
  menu,
  home,
  volumeUp,
  volumeDown,
  mute,
  channelUp,
  channelDown,
  number0,
  number1,
  number2,
  number3,
  number4,
  number5,
  number6,
  number7,
  number8,
  number9,
  unknown,
}

/// 遥控器按键事件
class RemoteKeyEvent {
  final RemoteKeyType keyType;
  final bool isLongPress;
  final DateTime timestamp;

  RemoteKeyEvent({
    required this.keyType,
    this.isLongPress = false,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  @override
  String toString() {
    return 'RemoteKeyEvent{keyType: $keyType, isLongPress: $isLongPress}';
  }
}

/// 遥控器控制服务
class RemoteControlService {
  static final Logger _logger = Logger();
  static final List<Function(RemoteKeyEvent)> _listeners = [];
  
  /// 添加按键监听器
  static void addListener(Function(RemoteKeyEvent) listener) {
    _listeners.add(listener);
  }

  /// 移除按键监听器
  static void removeListener(Function(RemoteKeyEvent) listener) {
    _listeners.remove(listener);
  }

  /// 清除所有监听器
  static void clearListeners() {
    _listeners.clear();
  }

  /// 处理键盘事件
  static KeyEventResult handleKeyEvent(KeyEvent event) {
    if (event is! KeyDownEvent) {
      return KeyEventResult.ignored;
    }

    final keyType = _mapLogicalKeyToRemoteKey(event.logicalKey);
    if (keyType == RemoteKeyType.unknown) {
      return KeyEventResult.ignored;
    }

    final remoteEvent = RemoteKeyEvent(
      keyType: keyType,
      isLongPress: false, // 简化处理，不检测长按
    );

    _logger.d('Remote key event: $remoteEvent');

    // 通知所有监听器
    for (final listener in _listeners) {
      try {
        listener(remoteEvent);
      } catch (e) {
        _logger.e('Error in remote key listener: $e');
      }
    }

    return KeyEventResult.handled;
  }

  /// 将逻辑按键映射到遥控器按键类型
  static RemoteKeyType _mapLogicalKeyToRemoteKey(LogicalKeyboardKey key) {
    switch (key) {
      // 方向键
      case LogicalKeyboardKey.arrowUp:
        return RemoteKeyType.up;
      case LogicalKeyboardKey.arrowDown:
        return RemoteKeyType.down;
      case LogicalKeyboardKey.arrowLeft:
        return RemoteKeyType.left;
      case LogicalKeyboardKey.arrowRight:
        return RemoteKeyType.right;

      // 确认键
      case LogicalKeyboardKey.select:
      case LogicalKeyboardKey.enter:
        return RemoteKeyType.center;

      // 返回键
      case LogicalKeyboardKey.escape:
      case LogicalKeyboardKey.goBack:
        return RemoteKeyType.back;

      // 菜单键
      case LogicalKeyboardKey.contextMenu:
      case LogicalKeyboardKey.f1: // 某些遥控器的菜单键映射为F1
        return RemoteKeyType.menu;

      // 主页键
      case LogicalKeyboardKey.home:
        return RemoteKeyType.home;

      // 音量键
      case LogicalKeyboardKey.audioVolumeUp:
        return RemoteKeyType.volumeUp;
      case LogicalKeyboardKey.audioVolumeDown:
        return RemoteKeyType.volumeDown;
      case LogicalKeyboardKey.audioVolumeMute:
        return RemoteKeyType.mute;

      // 频道键
      case LogicalKeyboardKey.channelUp:
        return RemoteKeyType.channelUp;
      case LogicalKeyboardKey.channelDown:
        return RemoteKeyType.channelDown;

      // 数字键
      case LogicalKeyboardKey.digit0:
        return RemoteKeyType.number0;
      case LogicalKeyboardKey.digit1:
        return RemoteKeyType.number1;
      case LogicalKeyboardKey.digit2:
        return RemoteKeyType.number2;
      case LogicalKeyboardKey.digit3:
        return RemoteKeyType.number3;
      case LogicalKeyboardKey.digit4:
        return RemoteKeyType.number4;
      case LogicalKeyboardKey.digit5:
        return RemoteKeyType.number5;
      case LogicalKeyboardKey.digit6:
        return RemoteKeyType.number6;
      case LogicalKeyboardKey.digit7:
        return RemoteKeyType.number7;
      case LogicalKeyboardKey.digit8:
        return RemoteKeyType.number8;
      case LogicalKeyboardKey.digit9:
        return RemoteKeyType.number9;

      default:
        return RemoteKeyType.unknown;
    }
  }
}

/// 遥控器导航混入类
mixin RemoteNavigationMixin<T extends StatefulWidget> on State<T> {
  final Logger _logger = Logger();
  
  @override
  void initState() {
    super.initState();
    RemoteControlService.addListener(_handleRemoteKeyEvent);
  }

  @override
  void dispose() {
    RemoteControlService.removeListener(_handleRemoteKeyEvent);
    super.dispose();
  }

  /// 处理遥控器按键事件（子类需要重写）
  void onRemoteKeyEvent(RemoteKeyEvent event) {
    // 默认实现，子类可以重写
  }

  void _handleRemoteKeyEvent(RemoteKeyEvent event) {
    if (mounted) {
      try {
        onRemoteKeyEvent(event);
      } catch (e) {
        _logger.e('Error handling remote key event: $e');
      }
    }
  }
}

/// 焦点管理器
class FocusManager {
  static final Logger _logger = Logger();
  static final Map<String, FocusNode> _focusNodes = {};
  static String? _currentFocusId;

  /// 注册焦点节点
  static void registerFocusNode(String id, FocusNode focusNode) {
    _focusNodes[id] = focusNode;
    _logger.d('Registered focus node: $id');
  }

  /// 注销焦点节点
  static void unregisterFocusNode(String id) {
    _focusNodes.remove(id);
    if (_currentFocusId == id) {
      _currentFocusId = null;
    }
    _logger.d('Unregistered focus node: $id');
  }

  /// 请求焦点
  static void requestFocus(String id) {
    final focusNode = _focusNodes[id];
    if (focusNode != null) {
      focusNode.requestFocus();
      _currentFocusId = id;
      _logger.d('Focus requested: $id');
    } else {
      _logger.w('Focus node not found: $id');
    }
  }

  /// 获取当前焦点ID
  static String? get currentFocusId => _currentFocusId;

  /// 获取所有注册的焦点节点ID
  static List<String> get registeredIds => _focusNodes.keys.toList();

  /// 清除所有焦点节点
  static void clear() {
    _focusNodes.clear();
    _currentFocusId = null;
    _logger.d('All focus nodes cleared');
  }
}

/// 长按检测器
class LongPressDetector {
  static final Logger _logger = Logger();
  static final Map<RemoteKeyType, DateTime> _pressStartTimes = {};
  static const Duration _longPressDuration = Duration(milliseconds: 800);

  /// 开始按键检测
  static void startPress(RemoteKeyType keyType) {
    _pressStartTimes[keyType] = DateTime.now();
  }

  /// 结束按键检测
  static bool endPress(RemoteKeyType keyType) {
    final startTime = _pressStartTimes.remove(keyType);
    if (startTime != null) {
      final duration = DateTime.now().difference(startTime);
      final isLongPress = duration >= _longPressDuration;
      _logger.d('Key $keyType pressed for ${duration.inMilliseconds}ms, isLongPress: $isLongPress');
      return isLongPress;
    }
    return false;
  }

  /// 检查是否为长按
  static bool isLongPress(RemoteKeyType keyType) {
    final startTime = _pressStartTimes[keyType];
    if (startTime != null) {
      final duration = DateTime.now().difference(startTime);
      return duration >= _longPressDuration;
    }
    return false;
  }

  /// 清除所有按键状态
  static void clear() {
    _pressStartTimes.clear();
  }
}
