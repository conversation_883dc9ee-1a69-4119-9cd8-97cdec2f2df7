import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/device_model.dart';
import '../services/hikvision_service.dart';

/// 设备和摄像头选择界面
class DeviceSelectionScreen extends StatefulWidget {
  final DeviceManager deviceManager;
  final Function(CameraChannelInfo) onChannelSelected;
  final VoidCallback onClose;

  const DeviceSelectionScreen({
    Key? key,
    required this.deviceManager,
    required this.onChannelSelected,
    required this.onClose,
  }) : super(key: key);

  @override
  State<DeviceSelectionScreen> createState() => _DeviceSelectionScreenState();
}

class _DeviceSelectionScreenState extends State<DeviceSelectionScreen> {
  int _selectedDeviceIndex = 0;
  int _selectedChannelIndex = 0;
  final FocusNode _focusNode = FocusNode();

  // 添加设备对话框的控制器
  final TextEditingController _ipController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _usernameController = TextEditingController(text: 'admin');
  final TextEditingController _passwordController = TextEditingController(text: '12345');
  bool _isAddingDevice = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _ipController.dispose();
    _nameController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// 显示添加设备对话框
  void _showAddDeviceDialog() {
    // 清空输入框
    _ipController.clear();
    _nameController.clear();
    _usernameController.text = 'admin';
    _passwordController.text = '12345';

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              backgroundColor: Colors.grey[900],
              title: const Text(
                '添加录像机',
                style: TextStyle(color: Colors.white),
              ),
              content: SizedBox(
                width: 400,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildInputField(
                      controller: _ipController,
                      label: 'IP地址',
                      hint: '例如: *************',
                      icon: Icons.router,
                    ),
                    const SizedBox(height: 16),
                    _buildInputField(
                      controller: _nameController,
                      label: '设备名称',
                      hint: '例如: 客厅录像机',
                      icon: Icons.device_hub,
                    ),
                    const SizedBox(height: 16),
                    _buildInputField(
                      controller: _usernameController,
                      label: '用户名',
                      hint: '默认: admin',
                      icon: Icons.person,
                    ),
                    const SizedBox(height: 16),
                    _buildInputField(
                      controller: _passwordController,
                      label: '密码',
                      hint: '设备登录密码',
                      icon: Icons.lock,
                      obscureText: true,
                    ),
                    if (_isAddingDevice) ...[
                      const SizedBox(height: 16),
                      const Row(
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 12),
                          Text(
                            '正在连接设备...',
                            style: TextStyle(color: Colors.white70),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: _isAddingDevice ? null : () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('取消'),
                ),
                ElevatedButton(
                  onPressed: _isAddingDevice ? null : () {
                    _addDevice(setState);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                  ),
                  child: Text(_isAddingDevice ? '连接中...' : '添加'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      onKeyEvent: _handleKeyEvent,
      child: Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: _buildDeviceList(),
                  ),
                  const SizedBox(width: 24),
                  Expanded(
                    flex: 2,
                    child: _buildChannelGrid(),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            _buildBottomBar(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Text(
          '选择摄像头',
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        // 添加设备按钮
        GestureDetector(
          onTap: _showAddDeviceDialog,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.7),
              borderRadius: BorderRadius.circular(6),
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.add, color: Colors.white, size: 20),
                SizedBox(width: 4),
                Text(
                  '添加设备',
                  style: TextStyle(color: Colors.white, fontSize: 14),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 16),
        GestureDetector(
          onTap: widget.onClose,
          child: const Icon(
            Icons.close,
            color: Colors.white,
            size: 32,
          ),
        ),
      ],
    );
  }

  Widget _buildDeviceList() {
    final devices = widget.deviceManager.connectedDevices;
    
    if (devices.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.device_hub,
                color: Colors.grey,
                size: 48,
              ),
              SizedBox(height: 16),
              Text(
                '未发现设备',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              '录像机列表',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: devices.length,
              itemBuilder: (context, index) {
                return _buildDeviceItem(devices[index], index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceItem(DeviceModel device, int index) {
    final isSelected = index == _selectedDeviceIndex;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedDeviceIndex = index;
          _selectedChannelIndex = 0;
        });
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue.withOpacity(0.3) : Colors.transparent,
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              device.device.name,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.grey[300],
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              device.device.ip,
              style: TextStyle(
                color: isSelected ? Colors.white70 : Colors.grey,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '${device.getEnabledChannels().length} 个通道',
              style: TextStyle(
                color: isSelected ? Colors.white70 : Colors.grey,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChannelGrid() {
    final devices = widget.deviceManager.connectedDevices;
    
    if (devices.isEmpty || _selectedDeviceIndex >= devices.length) {
      return Container(
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Text(
            '请先选择录像机',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
        ),
      );
    }

    final selectedDevice = devices[_selectedDeviceIndex];
    final channels = selectedDevice.getEnabledChannels();

    if (channels.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Text(
            '该设备没有可用通道',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              '摄像头通道 (${selectedDevice.device.name})',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: GridView.builder(
              padding: const EdgeInsets.all(16),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 2.5,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: channels.length,
              itemBuilder: (context, index) {
                return _buildChannelItem(
                  CameraChannelInfo(
                    device: selectedDevice,
                    channel: channels[index],
                  ),
                  index,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChannelItem(CameraChannelInfo channelInfo, int index) {
    final isSelected = index == _selectedChannelIndex;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedChannelIndex = index;
        });
      },
      onDoubleTap: () {
        widget.onChannelSelected(channelInfo);
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue.withOpacity(0.3) : Colors.black.withOpacity(0.3),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              children: [
                Icon(
                  Icons.videocam,
                  color: isSelected ? Colors.white : Colors.grey,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    channelInfo.channel.name,
                    style: TextStyle(
                      color: isSelected ? Colors.white : Colors.grey[300],
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              '通道 ${channelInfo.channel.id}',
              style: TextStyle(
                color: isSelected ? Colors.white70 : Colors.grey,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text(
          '使用方向键选择，回车键确认，ESC键退出',
          style: TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    if (event is! KeyDownEvent) {
      return KeyEventResult.ignored;
    }

    final devices = widget.deviceManager.connectedDevices;
    if (devices.isEmpty) {
      return KeyEventResult.ignored;
    }

    switch (event.logicalKey) {
      case LogicalKeyboardKey.arrowUp:
        _moveSelection(-1, 0);
        return KeyEventResult.handled;
        
      case LogicalKeyboardKey.arrowDown:
        _moveSelection(1, 0);
        return KeyEventResult.handled;
        
      case LogicalKeyboardKey.arrowLeft:
        _moveSelection(0, -1);
        return KeyEventResult.handled;
        
      case LogicalKeyboardKey.arrowRight:
        _moveSelection(0, 1);
        return KeyEventResult.handled;
        
      case LogicalKeyboardKey.enter:
      case LogicalKeyboardKey.select:
        _selectCurrentChannel();
        return KeyEventResult.handled;
        
      case LogicalKeyboardKey.escape:
      case LogicalKeyboardKey.goBack:
        widget.onClose();
        return KeyEventResult.handled;
        
      default:
        return KeyEventResult.ignored;
    }
  }

  void _moveSelection(int deviceDelta, int channelDelta) {
    setState(() {
      if (deviceDelta != 0) {
        final devices = widget.deviceManager.connectedDevices;
        _selectedDeviceIndex = (_selectedDeviceIndex + deviceDelta).clamp(0, devices.length - 1);
        _selectedChannelIndex = 0; // 重置通道选择
      }
      
      if (channelDelta != 0) {
        final devices = widget.deviceManager.connectedDevices;
        if (_selectedDeviceIndex < devices.length) {
          final channels = devices[_selectedDeviceIndex].getEnabledChannels();
          _selectedChannelIndex = (_selectedChannelIndex + channelDelta).clamp(0, channels.length - 1);
        }
      }
    });
  }

  void _selectCurrentChannel() {
    final devices = widget.deviceManager.connectedDevices;
    if (_selectedDeviceIndex < devices.length) {
      final selectedDevice = devices[_selectedDeviceIndex];
      final channels = selectedDevice.getEnabledChannels();
      if (_selectedChannelIndex < channels.length) {
        final channelInfo = CameraChannelInfo(
          device: selectedDevice,
          channel: channels[_selectedChannelIndex],
        );
        widget.onChannelSelected(channelInfo);
      }
    }
  }

  /// 构建输入框
  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    bool obscureText = false,
  }) {
    return TextField(
      controller: controller,
      obscureText: obscureText,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        labelStyle: const TextStyle(color: Colors.white70),
        hintStyle: const TextStyle(color: Colors.white54),
        prefixIcon: Icon(icon, color: Colors.white70),
        enabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: Colors.white54),
          borderRadius: BorderRadius.circular(8),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: Colors.blue),
          borderRadius: BorderRadius.circular(8),
        ),
        filled: true,
        fillColor: Colors.grey[800],
      ),
    );
  }

  /// 添加设备
  Future<void> _addDevice(StateSetter dialogSetState) async {
    final ip = _ipController.text.trim();
    final name = _nameController.text.trim();
    final username = _usernameController.text.trim();
    final password = _passwordController.text.trim();

    // 验证输入
    if (ip.isEmpty) {
      _showErrorSnackBar('请输入IP地址');
      return;
    }

    if (username.isEmpty) {
      _showErrorSnackBar('请输入用户名');
      return;
    }

    if (password.isEmpty) {
      _showErrorSnackBar('请输入密码');
      return;
    }

    // 验证IP格式
    if (!_isValidIP(ip)) {
      _showErrorSnackBar('请输入有效的IP地址');
      return;
    }

    dialogSetState(() {
      _isAddingDevice = true;
    });

    try {
      // 创建设备对象
      final device = HikVisionDevice(
        ip: ip,
        name: name.isEmpty ? 'NVR-$ip' : name,
        model: 'Unknown',
        serialNumber: 'Unknown',
        httpPort: 80,
      );

      // 添加到设备管理器
      widget.deviceManager.addDevice(device);

      // 获取设备模型并设置凭据
      final deviceModel = widget.deviceManager.getDevice(ip);
      if (deviceModel != null) {
        deviceModel.setCredentials(username, password);

        // 尝试连接
        final connected = await deviceModel.connect();

        if (connected) {
          // 连接成功，关闭对话框
          if (mounted) {
            Navigator.of(context).pop();
            _showSuccessSnackBar('设备添加成功！找到 ${deviceModel.channels.length} 个摄像头');
          }
        } else {
          // 连接失败，移除设备
          widget.deviceManager.removeDevice(ip);
          _showErrorSnackBar('无法连接到设备，请检查IP地址、用户名和密码');
        }
      }
    } catch (e) {
      _showErrorSnackBar('添加设备失败: $e');
    } finally {
      if (mounted) {
        dialogSetState(() {
          _isAddingDevice = false;
        });
      }
    }
  }

  /// 验证IP地址格式
  bool _isValidIP(String ip) {
    final parts = ip.split('.');
    if (parts.length != 4) return false;

    for (final part in parts) {
      final num = int.tryParse(part);
      if (num == null || num < 0 || num > 255) return false;
    }
    return true;
  }

  /// 显示错误提示
  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// 显示成功提示
  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}
